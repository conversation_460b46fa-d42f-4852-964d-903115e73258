use anyhow::anyhow;
use solana_sdk::{
    instruction::Instruction,
    signature::Keypair,
    signer::Signer,
    hash::Hash,
    system_instruction,
};

use super::nonce_cache::NonceCache;

/// 🚀 刷新nonce值 - 通过gRPC实时订阅自动更新，无需手动调用
/// 这个函数现在主要用作后备方案，实际更新通过gRPC订阅自动处理
pub async fn refresh_nonce_value() -> Result<(), anyhow::Error> {
    tracing::info!("🔄 Nonce值通过gRPC订阅自动更新，无需手动刷新");
    Ok(())
}

/// 添加nonce推进指令到指令集合中
/// 必须使用nonce账户池，不允许回退
pub fn add_nonce_instruction(
    instructions: &mut Vec<Instruction>,
    payer: &Keypair,
) -> Result<(), anyhow::Error> {
    use crate::services::nonce_pool::NonceAccountPool;

    // 必须使用nonce账户池
    let pool = NonceAccountPool::get_instance();
    if !pool.is_initialized() {
        return Err(anyhow!("❌ Nonce账户池未初始化，无法构建交易"));
    }

    if let Some(account) = pool.get_next_available_account() {
        // 创建nonce推进指令
        let nonce_advance_ix = system_instruction::advance_nonce_account(
            &account.address,
            &payer.pubkey(),
        );
        instructions.push(nonce_advance_ix);
        tracing::debug!("🚀 添加nonce池推进指令: {}", account.address);
        Ok(())
    } else {
        Err(anyhow!("❌ Nonce账户池中所有账户都在使用，无法构建交易"))
    }
}

/// 获取用于交易的blockhash
/// 必须使用nonce账户池，不允许回退
pub fn get_transaction_blockhash(_recent_blockhash: Hash) -> Hash {
    use crate::services::nonce_pool::NonceAccountPool;

    // 必须使用nonce账户池
    let pool = NonceAccountPool::get_instance();
    if !pool.is_initialized() {
        tracing::error!("❌ Nonce账户池未初始化，无法获取nonce值");
        panic!("Nonce账户池未初始化，系统无法正常工作");
    }

    if let Some(account) = pool.get_next_available_account() {
        tracing::info!("🚀 使用nonce账户池，账户: {}", account.address);
        tracing::info!("🚀 nonce值: {}", account.current_nonce);
        account.current_nonce
    } else {
        tracing::error!("❌ Nonce账户池中所有账户都在使用，无法获取nonce值");
        panic!("Nonce账户池中所有账户都在使用，系统无法正常工作");
    }
}

/// 检查是否使用nonce账户（必须使用nonce账户池）
pub fn is_using_nonce() -> bool {
    use crate::services::nonce_pool::NonceAccountPool;

    // 必须使用nonce账户池
    let pool = NonceAccountPool::get_instance();
    pool.is_initialized()
}

/// 释放nonce账户（用于交易确认后）
/// 必须使用nonce账户池
pub fn release_nonce_account(nonce_account: Option<&solana_sdk::pubkey::Pubkey>) {
    use crate::services::nonce_pool::NonceAccountPool;

    let pool = NonceAccountPool::get_instance();
    if !pool.is_initialized() {
        tracing::error!("❌ 尝试释放nonce账户但账户池未初始化");
        return;
    }

    if let Some(address) = nonce_account {
        pool.release_account(address);
        tracing::info!("🔓 释放nonce账户: {}", address);
    } else {
        tracing::warn!("⚠️ 尝试释放nonce账户但地址为空");
    }
}

/// 标记当前nonce为已使用（向后兼容）
/// 应在交易发送成功后立即调用
pub fn mark_nonce_as_used() {
    release_nonce_account(None);
}
