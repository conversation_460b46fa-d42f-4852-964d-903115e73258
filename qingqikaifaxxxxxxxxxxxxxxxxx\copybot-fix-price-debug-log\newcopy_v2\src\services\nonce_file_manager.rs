use serde::{Deserialize, Serialize};
use solana_sdk::{
    pubkey::Pubkey,
    hash::Hash,
    signature::Keypair,
    signer::Signer,
    system_instruction,
    transaction::Transaction,
};
use solana_client::nonblocking::rpc_client::RpcClient;
use anyhow::{Result, Context};
use tracing::{info, warn, error};
use std::{
    fs,
    path::PathBuf,
    sync::Arc,
};
use tokio::time::{sleep, Duration};

/// Nonce账户持久化数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NonceAccountData {
    pub address: String,
    pub nonce_value: String,
    pub authority: String,
    pub lamports: u64,
    pub is_initialized: bool,
}

/// Nonce文件管理器
#[derive(Debug, Serialize, Deserialize)]
pub struct NonceFileData {
    pub version: String,
    pub created_at: u64,
    pub updated_at: u64,
    pub accounts: Vec<NonceAccountData>,
}

impl Default for NonceFileData {
    fn default() -> Self {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        Self {
            version: "1.0.0".to_string(),
            created_at: now,
            updated_at: now,
            accounts: Vec::new(),
        }
    }
}

pub struct NonceFileManager {
    file_path: PathBuf,
    rpc_client: Arc<RpcClient>,
    payer: Arc<Keypair>,
}

impl NonceFileManager {
    /// 创建新的nonce文件管理器
    pub fn new(rpc_client: Arc<RpcClient>, payer: Arc<Keypair>) -> Self {
        // 文件存储在项目根目录
        let file_path = PathBuf::from("nonce_accounts.json");
        
        Self {
            file_path,
            rpc_client,
            payer,
        }
    }

    /// 检查nonce文件是否存在
    pub fn file_exists(&self) -> bool {
        self.file_path.exists()
    }

    /// 从文件加载nonce账户数据
    pub fn load_from_file(&self) -> Result<Vec<(Pubkey, Hash)>> {
        if !self.file_exists() {
            return Ok(Vec::new());
        }

        let file_content = fs::read_to_string(&self.file_path)
            .context("读取nonce文件失败")?;
        
        let file_data: NonceFileData = serde_json::from_str(&file_content)
            .context("解析nonce文件JSON失败")?;
        
        let mut accounts = Vec::new();
        
        for account_data in file_data.accounts {
            if let (Ok(address), Ok(nonce_value)) = (
                account_data.address.parse::<Pubkey>(),
                account_data.nonce_value.parse::<Hash>()
            ) {
                accounts.push((address, nonce_value));
                info!("📂 从文件加载nonce账户: {} (nonce: {})", address, nonce_value);
            } else {
                warn!("⚠️ 无效的nonce账户数据: {}", account_data.address);
            }
        }
        
        Ok(accounts)
    }

    /// 保存nonce账户数据到文件
    pub fn save_to_file(&self, accounts: &[(Pubkey, Hash)]) -> Result<()> {        
        let mut file_data = if self.file_exists() {
            // 如果文件存在，更新现有数据
            let file_content = fs::read_to_string(&self.file_path)?;
            serde_json::from_str::<NonceFileData>(&file_content)
                .unwrap_or_default()
        } else {
            NonceFileData::default()
        };
        
        // 更新时间戳
        file_data.updated_at = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        // 转换账户数据
        file_data.accounts.clear();
        for (address, nonce_value) in accounts {
            file_data.accounts.push(NonceAccountData {
                address: address.to_string(),
                nonce_value: nonce_value.to_string(),
                authority: self.payer.pubkey().to_string(),
                lamports: 0, // 实际值会在创建时获取
                is_initialized: true,
            });
        }
        
        // 写入文件
        let json_content = serde_json::to_string_pretty(&file_data)
            .context("序列化nonce数据失败")?;
        
        fs::write(&self.file_path, json_content)
            .context("写入nonce文件失败")?;
        
        Ok(())
    }

    /// 根据配置创建nonce账户
    pub async fn create_accounts_by_config(&self, target_count: usize) -> Result<Vec<(Pubkey, Hash)>> {
        if target_count == 0 {
            return Ok(Vec::new());
        }
        
        let mut created_accounts = Vec::new();
        
        // 每批创建5个账户
        const BATCH_SIZE: usize = 5;
        let batches = (target_count + BATCH_SIZE - 1) / BATCH_SIZE;
        
        for batch in 0..batches {
            let start_idx = batch * BATCH_SIZE;
            let end_idx = std::cmp::min(start_idx + BATCH_SIZE, target_count);
            let batch_count = end_idx - start_idx;
            
            match self.create_batch_accounts(batch_count).await {
                Ok(mut batch_accounts) => {
                    created_accounts.append(&mut batch_accounts);
                },
                Err(e) => {
                    error!("批次 {} 创建失败: {}", batch + 1, e);
                    // 继续创建下一批
                }
            }
            
            // 批次间等待
            if batch + 1 < batches {
                sleep(Duration::from_millis(500)).await;
            }
        }
        
        if !created_accounts.is_empty() {
            info!("💾 保存 {} 个新创建的nonce账户到文件", created_accounts.len());
            // 立即保存到文件
            self.save_to_file(&created_accounts)?;
            
            // 🚀 新增：立即更新账户池缓存
            info!("🔄 自动更新账户池缓存");
            use crate::services::nonce_pool::NonceAccountPool;
            let pool = NonceAccountPool::get_instance();
            
            // 如果池已经初始化，添加新账户
            if pool.is_initialized() {
                pool.add_accounts(created_accounts.clone());
                info!("✅ 成功添加 {} 个新账户到账户池缓存", created_accounts.len());
                
                // 🚀 新增：立即从RPC刷新新账户的nonce值
                info!("🔄 立即从RPC刷新新账户的nonce值");
                for (address, _) in &created_accounts {
                    match self.get_nonce_value(address).await {
                        Ok(current_nonce) => {
                            pool.update_account_nonce(address, current_nonce);
                            info!("✅ 刷新nonce账户 {} 的值: {}", address, current_nonce);
                        },
                        Err(e) => {
                            warn!("⚠️ 无法刷新nonce账户 {} 的值: {}", address, e);
                        }
                    }
                }
            } else {
                // 如果池未初始化，直接初始化
                pool.init_pool(created_accounts.clone());
                info!("✅ 成功初始化账户池缓存，包含 {} 个账户", created_accounts.len());
                
                // 🚀 新增：立即从RPC刷新所有账户的nonce值
                info!("🔄 立即从RPC刷新所有账户的nonce值");
                for (address, _) in &created_accounts {
                    match self.get_nonce_value(address).await {
                        Ok(current_nonce) => {
                            pool.update_account_nonce(address, current_nonce);
                            info!("✅ 刷新nonce账户 {} 的值: {}", address, current_nonce);
                        },
                        Err(e) => {
                            warn!("⚠️ 无法刷新nonce账户 {} 的值: {}", address, e);
                        }
                    }
                }
            }
        }
        
        Ok(created_accounts)
    }

    /// 创建一批nonce账户
    async fn create_batch_accounts(&self, count: usize) -> Result<Vec<(Pubkey, Hash)>> {
        let mut instructions = Vec::new();
        let mut nonce_keypairs = Vec::new();
        
        // 为每个账户创建指令
        for _ in 0..count {
            let nonce_keypair = Keypair::new();
            
            // 获取租金
            let rent = self.rpc_client.get_minimum_balance_for_rent_exemption(80).await?;
            
            // 创建nonce账户指令
            let create_instructions = system_instruction::create_nonce_account(
                &self.payer.pubkey(),
                &nonce_keypair.pubkey(),
                &self.payer.pubkey(),
                rent,
            );
            
            instructions.extend(create_instructions);
            nonce_keypairs.push(nonce_keypair);
        }
        
        // 构建和发送交易
        let recent_blockhash = self.rpc_client.get_latest_blockhash().await?;
        let mut transaction = Transaction::new_with_payer(&instructions, Some(&self.payer.pubkey()));
        
        let mut signers: Vec<&dyn solana_sdk::signer::Signer> = vec![&*self.payer];
        for nonce_keypair in &nonce_keypairs {
            signers.push(nonce_keypair);
        }
        
        transaction.sign(&signers, recent_blockhash);
        
        // 发送交易
        let signature = self.rpc_client.send_and_confirm_transaction(&transaction).await?;
        
        // 获取每个账户的nonce值
        let mut batch_accounts = Vec::new();
        
        // 等待账户确认
        sleep(Duration::from_millis(1000)).await;
        
        for nonce_keypair in nonce_keypairs {
            let nonce_pubkey = nonce_keypair.pubkey();
            
            // 尝试获取nonce账户状态
            match self.get_nonce_value(&nonce_pubkey).await {
                Ok(nonce_value) => {
                    batch_accounts.push((nonce_pubkey, nonce_value));
                },
                Err(e) => {
                    warn!("⚠️ 获取nonce账户 {} 状态失败: {}", nonce_pubkey, e);
                }
            }
        }
        
        Ok(batch_accounts)
    }

    /// 获取nonce账户的当前值
    pub async fn get_nonce_value(&self, nonce_account: &Pubkey) -> Result<Hash> {
        let account = self.rpc_client
            .get_account(nonce_account)
            .await
            .context("获取nonce账户信息失败")?;

        let nonce_data = solana_client::nonce_utils::data_from_account(&account)
            .context("解析nonce账户数据失败")?;

        Ok(nonce_data.blockhash())
    }

    /// 更新文件中的nonce值（可选，用于定期同步）
    pub async fn sync_nonce_values(&self) -> Result<()> {
        let accounts = self.load_from_file()?;
        if accounts.is_empty() {
            return Ok(());
        }
        
        info!("🔄 同步 {} 个nonce账户的值", accounts.len());
        
        let mut updated_accounts = Vec::new();
        
        for (address, _old_nonce) in accounts {
            match self.get_nonce_value(&address).await {
                Ok(new_nonce) => {
                    updated_accounts.push((address, new_nonce));
                },
                Err(e) => {
                    warn!("⚠️ 同步nonce账户 {} 失败: {}", address, e);
                }
            }
        }
        
        if !updated_accounts.is_empty() {
            self.save_to_file(&updated_accounts)?;
            info!("✅ Nonce值同步完成");
        }
        
        Ok(())
    }
}