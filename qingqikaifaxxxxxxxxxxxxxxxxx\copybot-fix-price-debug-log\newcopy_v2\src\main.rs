use anyhow::Result;
use crate::{
    hotpath::{
        filter::{Filter, FilterConfig},
        redis_subscriber::RedisSubscriber,
        transaction_builder::TransactionBuilder,
        bonk_builder::BonkTransactionBuilder,
    },
    blockhash_service::BlockhashService,
    config::Settings,
};
use std::sync::Arc;
// use tokio::sync::Mutex; // 🚀 添加Mutex支持 - 未使用

use tracing::{debug, error, info, warn};
use crate::logging::{init_logging, is_production_mode};
use arc_swap::ArcSwap;
use futures_util::stream::StreamExt;
use redis::AsyncCommands;
use solana_sdk::signature::{Keypair, Signer};
use bs58;
use std::time::Duration;
use services::transaction_sender::TransactionSender;
use services::ata_cache::AtaCache;
use services::api_server::start_api_server;
use crate::shared::constants::{FILTER_CONFIG_KEY, FILTER_UPDATE_CHANNEL};
use crate::services::trade_logger::TradeLogger;
use crate::services::price_sse_manager::PriceSseManager;
use crate::services::sol_price_oracle::SolPriceOracle;
use crate::services::trade_event_manager::TradeEventManager;
use crate::services::transaction_tracker::{TransactionTracker, TrackRequest};
use tokio::sync::mpsc;

// --- 引入新模块 ---
use crate::services::price_broadcast::PriceBroadcastManager;
use crate::services::sell_executor::SellExecutor;
use crate::services::bonk_sell_executor::BonkSellExecutor;
use crate::services::auto_suspend_manager::AutoSuspendManager;
use dashmap::DashMap;
use solana_sdk::pubkey::Pubkey;
use solana_client::nonblocking::rpc_client::RpcClient;

pub mod hotpath;
mod shared;
pub mod blockhash_service;
mod config;
mod services;
mod strategies;
mod logging;
mod protocols;

/// 监听配置更新的后台任务
async fn listen_for_filter_updates(
    redis_client: redis::Client,
    filter_container: Arc<ArcSwap<Filter>>,
) -> Result<()> {
    info!("启动筛选器配置更新监听器，订阅频道: {}", FILTER_UPDATE_CHANNEL);
    
    let mut pubsub_conn = redis_client.get_async_pubsub().await?;
    pubsub_conn.subscribe(FILTER_UPDATE_CHANNEL).await?;
    let mut msg_stream = pubsub_conn.on_message();

    let mut cmd_conn = redis_client.get_multiplexed_async_connection().await?;

    while let Some(msg) = msg_stream.next().await {
        let payload = msg.get_payload_bytes();
        if let Ok(command) = serde_json::from_slice::<crate::hotpath::filter::FilterCommand>(payload) {
            // 简化显示筛选命令，不显示详细的结构体内容
            match &command {
                crate::hotpath::filter::FilterCommand::UpdateWallet(wallet_config) => {
                    info!("正在应用钱包配置更新: {}", wallet_config.wallet_address);
                },
                crate::hotpath::filter::FilterCommand::RemoveWallet(address) => {
                    info!("正在移除钱包配置: {}", address);
                },
                crate::hotpath::filter::FilterCommand::AddCreator(creator) => {
                    info!("正在添加创建者: {}", creator);
                },
                crate::hotpath::filter::FilterCommand::RemoveCreator(creator) => {
                    info!("正在移除创建者: {}", creator);
                },
                crate::hotpath::filter::FilterCommand::SetMinPrice(price) => {
                    info!("正在设置最低价格: {}", price);
                },
                crate::hotpath::filter::FilterCommand::SetMaxPrice(price) => {
                    info!("正在设置最高价格: {}", price);
                },
                _ => {
                    info!("正在应用筛选器配置更新");
                }
            }
            
            let mut retries = 5;
            while retries > 0 {
                let _: () = redis::cmd("WATCH").arg(FILTER_CONFIG_KEY).query_async(&mut cmd_conn).await?;
                
                let config_json: Option<String> = cmd_conn.get(FILTER_CONFIG_KEY).await?;
                
                let current_config: FilterConfig = config_json
                    .and_then(|json| serde_json::from_str(&json).ok())
                    .unwrap_or_default();

                let new_config = current_config.apply_command(command.clone());
                let new_config_json = serde_json::to_string(&new_config)?;

                let result: Option<()> = redis::pipe()
                    .atomic()
                    .set(FILTER_CONFIG_KEY, &new_config_json)
                    .query_async(&mut cmd_conn)
                    .await?;

                if result.is_some() {
                    filter_container.store(Arc::new(Filter::new(new_config)));
                    info!("筛选器配置已原子化更新并持久化。");
                    break; 
                } else {
                    warn!("配置键被并发修改，正在重试更新... (剩余次数: {})", retries - 1);
                    retries -= 1;
                    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
                }
            }
            if retries == 0 {
                error!("更新配置失败，已达到最大重试次数。");
            }
        } else {
            error!("反序列化筛选器命令失败. Payload: {}", String::from_utf8_lossy(payload));
        }
    }
    warn!("筛选器更新监听器已停止。");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // --- 日志初始化 ---
    let is_production = is_production_mode();
    let log_sse_manager = init_logging(is_production);

    // --- 0. 加载配置 ---
    let settings = Settings::new().expect("加载配置文件 settings.toml 失败");
    
    // --- 0.1. 初始化全局配置 ---
    shared::global_config::initialize_accelerator_config(settings.accelerator.clone());
    info!("全局加速器配置已初始化。启用状态: {}", shared::global_config::get_accelerator_config().enabled);
    
    info!("配置文件加载成功."); // 精简日志

    info!("正在初始化程序...");

    // --- 0.5. 初始化钱包和RPC客户端 ---
    // 从bs58编码的私钥字符串加载钱包
    let private_key_str = &settings.wallet.private_key_bs58;
    let keypair_bytes = bs58::decode(private_key_str)
        .into_vec()
        .expect("无效的bs58私钥字符串");
    let wallet_keypair = Arc::new(Keypair::from_bytes(&keypair_bytes).expect("无法从字节码创建密钥对"));
    info!("钱包已加载, 公钥: {}", wallet_keypair.pubkey());

    // 创建RPC客户端
    let rpc_client = Arc::new(RpcClient::new(settings.rpc.url.clone()));

    // --- 1. 【新架构】完整的nonce管理：检查→创建→初始化 ---
    {
        let target_pool_size = settings.nonce_pool.pool_size;
        info!("🔍 检查nonce文件状态，目标账户数量: {}", target_pool_size);

        use crate::services::nonce_pool_manager::NoncePoolManager;
        let nonce_pool_manager = NoncePoolManager::new(rpc_client.clone(), wallet_keypair.clone());

        match nonce_pool_manager.ensure_nonce_file_exists(target_pool_size).await {
            Ok(true) => {},
            Ok(false) => {
                error!("❌ 无法创建nonce账户，程序退出");
                return Err(anyhow::anyhow!("Nonce账户创建失败"));
            }
            Err(e) => {
                error!("❌ Nonce文件检查失败: {}", e);
                return Err(e.into());
            }
        }

        // 立即从文件加载到内存池，并从RPC刷新最新nonce值
        match nonce_pool_manager.load_from_file_to_pool().await {
            Ok(()) => {},
            Err(e) => {
                error!("❌ 从文件加载nonce账户失败: {}", e);
                return Err(e.into());
            }
        }
    }

    // --- 2. 初始化区块哈希服务（包含nonce订阅）---
    info!("正在初始化区块哈希服务...");
    let blockhash_service = Arc::new(BlockhashService::new());
    
    // 🚀 直接启动完整的哈希+nonce订阅
    let blockhash_service_clone = blockhash_service.clone();
    let grpc_endpoints = settings.grpc.endpoints.clone();
    tokio::spawn(async move {
        blockhash_service_clone.start(grpc_endpoints).await;
    });
    info!("区块哈希服务已启动（包含nonce订阅）。");

    // --- 1.5. 等待区块哈希服务预热 ---
    info!("正在等待区块哈希服务预热，最长等待30秒...");
    let preheat_timeout = Duration::from_secs(30);
    let preheat_result = tokio::time::timeout(preheat_timeout, async {
        loop {
            if blockhash_service.get_latest_blockhash().is_some() {
                info!("✅ 区块哈希服务已成功预热！");
                break;
            }
            tokio::time::sleep(Duration::from_millis(200)).await;
        }
    }).await;

    if preheat_result.is_err() {
        panic!("错误：区块哈希服务在 {} 秒内未能预热成功，程序即将退出。", preheat_timeout.as_secs());
    }

    // --- 2. 初始化Redis和筛选器配置 ---
    let client = redis::Client::open(settings.redis.url.as_str())?; // 从配置中获取
    let mut con = client.get_multiplexed_async_connection().await?;

    let json_str: Option<String> = con.get(FILTER_CONFIG_KEY).await?;
    let initial_config: FilterConfig = match json_str {
        Some(s) => {
            serde_json::from_str(&s).unwrap_or_else(|e| {
                warn!("从Redis解析配置失败: {}, 使用默认配置。", e);
                FilterConfig::default()
            })
        }
        None => {
            info!("在Redis中未找到配置，创建并存储默认配置。");
            let config = FilterConfig::default();
            let _: () = con.set(FILTER_CONFIG_KEY, serde_json::to_string(&config)?).await?;
            config
        }
    };
    info!("筛选器初始配置加载完成，共配置 {} 个钱包", initial_config.wallet_configs.len());
    debug!("筛选器详细配置: {:?}", initial_config);
    
    // --- 3. 初始化交易构建器 ---
    let transaction_builder = Arc::new(TransactionBuilder::new(
        wallet_keypair.clone(),
        blockhash_service.clone(), // 注入BlockhashService
        settings.rpc.blockhash_lag_slots,
        settings.fee_config.clone(), // 传递手续费配置
    ));

    // --- 4.1. 🚀 初始化无锁高性能Bonk构建器 (基于Pump协议优化原理) ---
    let bonk_transaction_builder = Arc::new(BonkTransactionBuilder::new(
        wallet_keypair.clone(),
        blockhash_service.clone(), // 复用同一个BlockhashService
        settings.rpc.blockhash_lag_slots,
        80_000,  // 默认计算单元限制
        100_000, // 默认优先费用
    ));  // 🚀 移除Mutex包装，直接Arc包装
    
    // --- 3.5. 初始化交易发送器 ---
    let transaction_sender = Arc::new(TransactionSender::new(&settings.rpc.url, settings.rpc.skip_preflight, settings.rpc.keep_alive_secs)?);

    // --- 3.6. 初始化 ATA 缓存 ---
    let ata_cache = Arc::new(AtaCache::new(settings.ata.cache_ttl_secs));
    
    // --- 3.7. 初始化核心业务服务 ---
    // 价格推送服务 (SSE)
    let price_sse_manager = PriceSseManager::new();
    // 交易事件推送服务 (SSE)
    let trade_event_manager = TradeEventManager::new();
    
    // SOL/USD 价格预言机
    let sol_price_oracle = SolPriceOracle::new();
    sol_price_oracle.start().await; // 启动后台更新任务

    // 自动暂停管理器
    let auto_suspend_manager = Arc::new(AutoSuspendManager::new(client.clone()));

    // --- 3.7.1 创建交易跟踪通道 ---
    let (tracker_tx, tracker_rx) = mpsc::channel::<TrackRequest>(1024);

    // --- 新架构：初始化 PriceBroadcastManager 和 SellExecutor ---
    let price_broadcast_manager = PriceBroadcastManager::new();

    // --- 新增：价格广播桥接任务 ---
    // 这个任务订阅内部价格广播，并将其转发给外部SSE管理器
    let mut price_receiver = price_broadcast_manager.subscribe();
    let sse_price_broadcaster = price_sse_manager.clone();
    tokio::spawn(async move {
        info!("价格广播桥接任务已启动。");
        while let Ok((mint, price)) = price_receiver.recv().await {
            sse_price_broadcaster
                .lock()
                .unwrap()
                .broadcast(&mint.to_string(), price);
        }
        warn!("价格广播桥接任务已停止。");
    });

    // 创建共享的"已撤销Mint"列表，现在是计数器
    let revoked_mints = Arc::new(DashMap::<Pubkey, u64>::new());

    let sell_executor = SellExecutor::new(
        transaction_builder.clone(),
        transaction_sender.clone(),
        tracker_tx.clone(),
        sol_price_oracle.clone(),
    );

    // 初始化BonkSellExecutor (使用预创建的高性能构建器)
    // 创建一个新的不带Mutex的BonkTransactionBuilder实例
    let bonk_builder = BonkTransactionBuilder::new(
        wallet_keypair.clone(),
        blockhash_service.clone(),
        settings.rpc.blockhash_lag_slots,
        80_000,  // 默认计算单元限制
        100_000, // 默认优先费用
    );
    
    let bonk_sell_executor = BonkSellExecutor::new(
        transaction_sender.clone(),
        tracker_tx.clone(),
        sol_price_oracle.clone(),
        wallet_keypair.clone(), // 直接使用wallet_keypair
        Arc::new(RpcClient::new(settings.rpc.url.clone())),
        80_000,  // 默认compute_unit_limit
        100_000, // 默认priority_fee (microlamports)
        Arc::new(bonk_builder), // 传递预创建的高性能构建器的Arc包装版本
        blockhash_service.clone(), // 新增：BlockhashService
        settings.rpc.blockhash_lag_slots, // 新增：区块哈希滞后槽数
    );

    // --- 启动交易跟踪服务 ---
    let transaction_tracker = TransactionTracker::new(
        trade_event_manager.clone(),
        sol_price_oracle.clone(),
        &settings.rpc.url,
        settings.transaction_tracker.confirmation_timeout_ms,
        sell_executor.clone(),
        Some(bonk_sell_executor),
        price_broadcast_manager.clone(),
        revoked_mints.clone(), // 正确注入到 Tracker
        auto_suspend_manager.clone(),
    );

    // 使用外部通道启动
    transaction_tracker.start_with_receiver(tracker_rx);

    // --- 3.8. 初始化交易历史记录器 ---
    let (trade_logger, trade_log_tx) = TradeLogger::new();
    tokio::spawn(trade_logger.start());

    // --- 4. 初始化并启动服务 ---
    let redis_subscriber = RedisSubscriber::new(
        &settings.redis.url,
        transaction_builder,
        bonk_transaction_builder, // 传递预创建的Bonk构建器
        transaction_sender,
        ata_cache,
        trade_log_tx,
        transaction_tracker,
        tracker_tx,
        price_broadcast_manager,
    ).await?;

    let filter_container = Arc::new(ArcSwap::new(Arc::new(Filter::new(initial_config))));

    tokio::spawn(listen_for_filter_updates(
        client.clone(),
        filter_container.clone(),
    ));

    // --- 5. 准备启动核心服务 ---
    // 先为 API 服务器克隆一个 filter_container 的 Arc 副本
    let api_filter_container = Arc::clone(&filter_container);
    let api_redis_client = client.clone();
    
    // 将 Redis 订阅服务也放入后台任务，防止其阻塞 main 函数
    // 这会将 filter_container 的所有权移入任务
    tokio::spawn(async move {
        if let Err(e) = redis_subscriber.start_subscription(filter_container).await {
            error!("Redis 订阅服务发生严重错误: {}", e);
        }
    });
    info!("所有后台服务已启动。");

    // --- 9. 启动API服务器 ---
    // 这是程序的最后一个前台任务。
    // 我们直接 `await` 它，这将阻塞 main 函数，使其不会提前退出。
    // 整个程序将在这里一直运行，直到API服务器因故停止或收到外部的终止信号。
    info!("正在启动API服务器作为前台服务...");
    start_api_server(
        api_filter_container,
        api_redis_client,
        price_sse_manager, 
        trade_event_manager,
        log_sse_manager,
        settings.auth.clone(),
    ).await;

    info!("API服务器已停止，程序即将退出。");

    Ok(())
}
