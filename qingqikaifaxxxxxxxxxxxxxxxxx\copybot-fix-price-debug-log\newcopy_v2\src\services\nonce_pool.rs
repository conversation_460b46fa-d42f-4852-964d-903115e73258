use solana_sdk::{pubkey::Pubkey, hash::Hash};
use std::sync::{Mutex, OnceLock, atomic::{AtomicUsize, AtomicBool, Ordering}};
use std::time::Instant;
use tracing::{info, warn, error};

/// 单个Nonce账户信息
#[derive(Clone)]
pub struct NonceAccountInfo {
    /// nonce账户地址
    pub address: Pubkey,
    /// 当前nonce值
    pub current_nonce: Hash,
    /// 最后更新时间
    pub last_updated: Instant,
}

/// Nonce账户池管理器
pub struct NonceAccountPool {
    /// 所有nonce账户信息
    accounts: Mutex<Vec<NonceAccountInfo>>,
    /// 当前轮换索引
    current_index: AtomicUsize,
    /// 每个账户的可用状态 (true=可用, false=正在使用)
    account_states: Mutex<Vec<AtomicBool>>,
    /// 配置的池大小
    configured_pool_size: AtomicUsize,
}

// 使用静态OnceLock确保单例
static NONCE_POOL: OnceLock<NonceAccountPool> = OnceLock::new();

impl NonceAccountPool {
    /// 获取单例实例
    pub fn get_instance() -> &'static NonceAccountPool {
        NONCE_POOL.get_or_init(|| {
            NonceAccountPool {
                accounts: Mutex::new(Vec::new()),
                current_index: AtomicUsize::new(0),
                account_states: Mutex::new(Vec::new()),
                configured_pool_size: AtomicUsize::new(10), // 默认10个
            }
        })
    }

    /// 设置池大小配置
    pub fn set_pool_size(&self, size: usize) {
        self.configured_pool_size.store(size, Ordering::Relaxed);
        info!("🚀 设置nonce账户池大小: {}", size);
    }

    /// 获取配置的池大小
    pub fn get_configured_pool_size(&self) -> usize {
        self.configured_pool_size.load(Ordering::Relaxed)
    }

    /// 初始化nonce账户池
    pub fn init_pool(&self, accounts: Vec<(Pubkey, Hash)>) {
        let mut pool_accounts = self.accounts.lock().unwrap();
        let mut states = self.account_states.lock().unwrap();
        
        pool_accounts.clear();
        states.clear();
        
        for (address, nonce) in accounts {
            pool_accounts.push(NonceAccountInfo {
                address,
                current_nonce: nonce,
                last_updated: Instant::now(),
            });
            states.push(AtomicBool::new(true)); // 初始都可用
            info!("💾 添加nonce账户到池: {} (nonce: {})", address, nonce);
        }
        
        info!("🚀 初始化nonce账户池完成，共 {} 个账户", pool_accounts.len());
    }

    /// 检查账户池是否已初始化
    pub fn is_initialized(&self) -> bool {
        let accounts = self.accounts.lock().unwrap();
        !accounts.is_empty()
    }

    /// 添加新账户到现有池中
    pub fn add_accounts(&self, new_accounts: Vec<(Pubkey, Hash)>) {
        let mut pool_accounts = self.accounts.lock().unwrap();
        let mut states = self.account_states.lock().unwrap();
        
        for (address, nonce) in new_accounts {
            pool_accounts.push(NonceAccountInfo {
                address,
                current_nonce: nonce,
                last_updated: Instant::now(),
            });
            states.push(AtomicBool::new(true)); // 初始都可用
        }
        
        info!("➕ 添加账户到池中，当前总数: {}", pool_accounts.len());
    }

    /// 获取下一个可用的nonce账户
    pub fn get_next_available_account(&self) -> Option<NonceAccountInfo> {
        let accounts = self.accounts.lock().unwrap();
        let states = self.account_states.lock().unwrap();
        
        if accounts.is_empty() {
            warn!("⚠️ Nonce账户池为空");
            return None;
        }

        let pool_size = accounts.len();
        info!("🎯 尝试获取可用nonce账户，池大小: {}", pool_size);
        
        // 尝试找到一个可用的账户，最多尝试池大小次数
        for _ in 0..pool_size {
            let index = self.current_index.fetch_add(1, Ordering::Relaxed) % pool_size;
            
            // 检查该账户是否可用
            if states[index].compare_exchange(true, false, Ordering::Relaxed, Ordering::Relaxed).is_ok() {
                // 成功获取到可用账户
                if let Some(account) = accounts.get(index) {
                    info!("🎯 选择nonce账户#{}: {} (nonce: {})", index, account.address, account.current_nonce);
                    return Some(account.clone());
                }
            }
        }

        // 所有nonce账户都在使用中，不能强制使用任何账户
        // 这会导致nonce重复使用而导致交易失败
        warn!("⚠️ 所有nonce账户都在使用中，无法获取可用账户");
        None
    }

    /// 释放指定地址的nonce账户
    pub fn release_account(&self, address: &Pubkey) {
        let accounts = self.accounts.lock().unwrap();
        let states = self.account_states.lock().unwrap();
        
        for (i, account) in accounts.iter().enumerate() {
            if account.address == *address {
                states[i].store(true, Ordering::Relaxed);
                info!("🔓 释放nonce账户#{}: {}", i, address);
                return;
            }
        }
        warn!("⚠️ 尝试释放未知的nonce账户: {}", address);
    }

    /// 更新指定地址的nonce值
    pub fn update_account_nonce(&self, address: &Pubkey, new_nonce: Hash) {
        let mut accounts = self.accounts.lock().unwrap();
        let states = self.account_states.lock().unwrap();
        
        for (i, account) in accounts.iter_mut().enumerate() {
            if account.address == *address {
                let old_nonce = account.current_nonce;
                account.current_nonce = new_nonce;
                account.last_updated = Instant::now();
                
                // 更新后自动释放该账户
                states[i].store(true, Ordering::Relaxed);
                
                info!("🔄 更新nonce账户#{}: {} (nonce: {} -> {})", i, address, old_nonce, new_nonce);
                return;
            }
        }
        warn!("⚠️ 尝试更新未知的nonce账户: {}", address);
    }

    /// 获取所有账户地址（用于gRPC订阅）
    pub fn get_all_addresses(&self) -> Vec<Pubkey> {
        let accounts = self.accounts.lock().unwrap();
        accounts.iter().map(|account| account.address).collect()
    }

    /// 获取池状态信息
    pub fn get_pool_status(&self) -> String {
        let accounts = self.accounts.lock().unwrap();
        let states = self.account_states.lock().unwrap();
        
        let available_count = states.iter()
            .map(|state| if state.load(Ordering::Relaxed) { 1 } else { 0 })
            .sum::<usize>();
        
        format!("Nonce池状态: {}/{} 可用 (配置大小: {})", 
                available_count, accounts.len(), self.get_configured_pool_size())
    }

    /// 检查是否需要创建更多账户
    pub fn needs_more_accounts(&self) -> usize {
        let accounts = self.accounts.lock().unwrap();
        let current_count = accounts.len();
        let target_count = self.get_configured_pool_size();
        
        if current_count < target_count {
            target_count - current_count
        } else {
            0
        }
    }

    /// 获取指定地址的nonce值（用于文件同步）
    pub fn get_nonce_for_address(&self, address: &Pubkey) -> Option<Hash> {
        let accounts = self.accounts.lock().unwrap();
        for account_info in accounts.iter() {
            if account_info.address == *address {
                return Some(account_info.current_nonce);
            }
        }
        None
    }
}
