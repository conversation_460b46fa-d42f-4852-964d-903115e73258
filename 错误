PS D:\personal\Desktop\hebing> cd qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2; $env:RUST_LOG = "info"; cargo run --release
   Compiling newcopy_v2 v0.1.0 (D:\personal\Desktop\hebing\qingqikaifaxxxxxxxxxxxxxxxxx\copybot-fix-price-debug-log\newcopy_v2)
error[E0412]: cannot find type `Transaction` in this scope
   --> src\hotpath\redis_subscriber.rs:577:78
    |
577 | ...   let transactions_array: [Transaction; 4] = signed_transactions.try_...
    |                                ^^^^^^^^^^^ not found in this scope
    |
help: consider importing one of these structs
    |
1   + use solana_sdk::transaction::Transaction;
    |
1   + use yellowstone_grpc_proto::prelude::Transaction;
    |

warning: unused import: `info`
 --> src\hotpath\bonk_filter.rs:5:15
  |
5 | use tracing::{info, debug};
  |               ^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `std::str::FromStr`
  --> src\hotpath\transaction_builder.rs:20:5
   |
20 | use std::str::FromStr;
   |     ^^^^^^^^^^^^^^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\hotpath\transaction_builder.rs:350:45
    |
350 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\hotpath\bonk_builder.rs:163:45
    |
163 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `commitment_config::CommitmentConfig`
  --> src\services\bonk_sell_executor.rs:14:5
   |
14 |     commitment_config::CommitmentConfig,
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `debug` and `warn`
  --> src\services\bonk_sell_executor.rs:21:28
   |
21 | use tracing::{error, info, debug, warn};
   |                            ^^^^^  ^^^^

warning: unused import: `get_transaction_blockhash`
   --> src\services\bonk_sell_executor.rs:346:45
    |
346 | ...   use crate::services::nonce_helper::{get_transaction_blockhash, is_u... 
    |                                           ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `uuid`
  --> src\services\senders\blockrazor_sender.rs:12:5
   |
12 | use uuid;
   |     ^^^^

warning: unused import: `tracing::info`
  --> src\services\nonce_scanner.rs:12:5
   |
12 | use tracing::info;
   |     ^^^^^^^^^^^^^

warning: unused import: `Hash`
  --> src\services\nonce_scanner.rs:46:42
   |
46 |             use solana_sdk::hash::{hash, Hash};
   |                                          ^^^^

warning: unused import: `Hash`
  --> src\services\nonce_scanner.rs:78:42
   |
78 |             use solana_sdk::hash::{hash, Hash};
   |                                          ^^^^

warning: unused import: `super::nonce_cache::NonceCache`
  --> src\services\nonce_helper.rs:10:5
   |
10 | use super::nonce_cache::NonceCache;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `error`
 --> src\services\nonce_pool.rs:4:27
  |
4 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused imports: `hash::Hash` and `pubkey::Pubkey`
 --> src\services\nonce_pool_manager.rs:2:5
  |
2 |     pubkey::Pubkey,
  |     ^^^^^^^^^^^^^^
3 |     hash::Hash,
  |     ^^^^^^^^^^

warning: unused import: `error`
 --> src\services\nonce_pool_manager.rs:8:27
  |
8 | use tracing::{info, warn, error};
  |                           ^^^^^

warning: unused variable: `signature`
   --> src\hotpath\redis_subscriber.rs:159:25
    |
159 |         let (signed_tx, signature, _blockhash) = match self.bonk_transact... 
    |                         ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`
    |
    = note: `#[warn(unused_variables)]` on by default

error[E0063]: missing fields `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field in initializer of `AcceleratorConfig`
   --> src\services\transaction_sender.rs:148:32
    |
148 |         let astralane_config = AcceleratorConfig {
    |                                ^^^^^^^^^^^^^^^^^ missing `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field

error[E0063]: missing fields `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field in initializer of `AcceleratorConfig`
   --> src\services\transaction_sender.rs:157:33
    |
157 |         let blockrazor_config = AcceleratorConfig {
    |                                 ^^^^^^^^^^^^^^^^^ missing `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field

error[E0063]: missing fields `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field in initializer of `AcceleratorConfig`
   --> src\services\transaction_sender.rs:166:33
    |
166 |         let flashblock_config = AcceleratorConfig {
    |                                 ^^^^^^^^^^^^^^^^^ missing `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field

error[E0063]: missing fields `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field in initializer of `AcceleratorConfig`
   --> src\services\transaction_sender.rs:175:28
    |
175 |         let oslot_config = AcceleratorConfig {
    |                            ^^^^^^^^^^^^^^^^^ missing `astralane_api_url`, `blockrazor_api_url`, `flashblock_api_url` and 1 other field

error[E0599]: no method named `clone` found for struct `senders::rpc_sender::RpcSender` in the current scope
   --> src\services\transaction_sender.rs:184:103
    |
184 | ...fallback_sender.clone(), true);
    |                    ^^^^^ method not found in `RpcSender`
    |
   ::: src\services\senders\rpc_sender.rs:19:1
    |
19  | pub struct RpcSender {
    | -------------------- method `clone` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `clone`, perhaps you need to implement it:
            candidate #1: `Clone`

error[E0599]: no method named `clone` found for struct `senders::rpc_sender::RpcSender` in the current scope
   --> src\services\transaction_sender.rs:185:106
    |
185 | ...fallback_sender.clone(), true);
    |                    ^^^^^ method not found in `RpcSender`
    |
   ::: src\services\senders\rpc_sender.rs:19:1
    |
19  | pub struct RpcSender {
    | -------------------- method `clone` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `clone`, perhaps you need to implement it:
            candidate #1: `Clone`

error[E0599]: no method named `clone` found for struct `senders::rpc_sender::RpcSender` in the current scope
   --> src\services\transaction_sender.rs:186:106
    |
186 | ...fallback_sender.clone(), true);
    |                    ^^^^^ method not found in `RpcSender`
    |
   ::: src\services\senders\rpc_sender.rs:19:1
    |
19  | pub struct RpcSender {
    | -------------------- method `clone` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `clone`, perhaps you need to implement it:
            candidate #1: `Clone`

warning: unused variable: `signature`
   --> src\services\nonce_file_manager.rs:268:13
    |
268 |         let signature = self.rpc_client.send_and_confirm_transaction(&tra... 
    |             ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_signature`

warning: value assigned to `price_before` is never read
  --> src\protocols\bonk\parser.rs:38:13
   |
38 |     let mut price_before = String::new();
   |             ^^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?
   = note: `#[warn(unused_assignments)]` on by default

warning: value assigned to `price_after` is never read
  --> src\protocols\bonk\parser.rs:39:13
   |
39 |     let mut price_after = String::new();
   |             ^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `slippage` is never read
  --> src\protocols\bonk\parser.rs:40:13
   |
40 |     let mut slippage = String::new();
   |             ^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

warning: value assigned to `actual_trade_price` is never read
  --> src\protocols\bonk\parser.rs:41:13
   |
41 |     let mut actual_trade_price = String::new();
   |             ^^^^^^^^^^^^^^^^^^
   |
   = help: maybe it is overwritten before being read?

Some errors have detailed explanations: E0063, E0412, E0599.
For more information about an error, try `rustc --explain E0063`.
warning: `newcopy_v2` (bin "newcopy_v2") generated 21 warnings
error: could not compile `newcopy_v2` (bin "newcopy_v2") due to 8 previous errors; 
21 warnings emitted
PS D:\personal\Desktop\hebing\qingqikaifaxxxxxxxxxxxxxxxxx\copybot-fix-price-debug-log\newcopy_v2>