use anyhow::Result;
use reqwest::Client;
use serde::Deserialize;
use solana_sdk::transaction::Transaction;
use std::time::Duration;
use tracing::{info, warn};
use std::cell::RefCell;
use reqwest::header::CONTENT_TYPE;

use crate::shared::global_config::get_accelerator_config;
use crate::services::senders::{
    astralane_sender::AstralaneSender,
    blockrazor_sender::BlockRazorSender,
    flashblock_sender::FlashblockSender,
    oslot_sender::OslotSender,
    rpc_sender::RpcSender,
    TransactionSenderBackend
};

/// 匹配 Flashblock API 成功响应的结构体
#[derive(Deserialize, Debug)]
#[allow(dead_code)]
struct FlashblockData {
    signatures: Vec<String>,
}

#[derive(Deserialize, Debug)]
#[allow(dead_code)]
struct FlashblockResponse {
    success: bool,
    data: Option<FlashblockData>,
    message: Option<String>,
}

thread_local! {
    // 交易 bincode 序列化缓冲区，线程本地重用，避免频繁分配
    static SER_BUF: RefCell<Vec<u8>> = RefCell::new(Vec::with_capacity(2048));
    // Base64 编码目标缓冲区，同样线程本地复用
    static B64_BUF: RefCell<String> = RefCell::new(String::with_capacity(4096));
    // JSON 请求体缓冲区
    static JSON_BUF: RefCell<String> = RefCell::new(String::with_capacity(8192));
}

/// 高性能交易发送器，负责将已签名交易通过动态选择的后端（加速器或标准RPC）发送出去。
#[derive(Clone)]
pub struct TransactionSender {
    backend: std::sync::Arc<dyn TransactionSenderBackend>,
}

impl TransactionSender {
    /// 构造函数，创建带有连接池的 HTTP 客户端，并根据配置初始化正确的交易发送后端。
    pub fn new(rpc_url: &str, skip_preflight: bool, keep_alive_secs: u64) -> Result<Self> {
        let client = Client::builder()
            .pool_idle_timeout(Some(Duration::from_secs(60)))  // 60秒后主动断开空闲连接
            .pool_max_idle_per_host(8)                         // 限制空闲连接数
            .tcp_keepalive(Some(Duration::from_secs(30)))      // TCP keepalive  
            .build()?;

        // ---- 预热与保活任务 ----
        let rpc_url_clone = rpc_url.to_string();
        let client_clone = client.clone();
        tokio::spawn(async move {
            if let Err(e) = send_health_ping(&client_clone, &rpc_url_clone).await {
                tracing::warn!("RPC预热失败: {e}");
            } else {
                tracing::info!("✅ RPC连接预热成功，已完成TLS握手");
            }
            
            let interval = Duration::from_secs(keep_alive_secs.max(5));
            loop {
                tokio::time::sleep(interval).await;
                
                // RPC保活
                if let Err(e) = send_health_ping(&client_clone, &rpc_url_clone).await {
                    tracing::warn!("RPC保活失败: {e}");
                }
                
                // 加速器保活
                let accel_config = get_accelerator_config();
                if accel_config.enabled {
                    if let Some((api_url, _)) = accel_config.get_active_provider_config() {
                        match send_accelerator_ping(&client_clone, &api_url).await {
                            Ok(()) => tracing::debug!("✅ 加速器保活成功: {}", api_url),
                            Err(e) => tracing::warn!("❌ 加速器保活失败 ({}): {e}", api_url),
                        }
                    }
                }
            }
        });

        // ---- 根据配置决定后端实现 ----
        let backend: Box<dyn TransactionSenderBackend> = {
            let accel_config = get_accelerator_config();
            if accel_config.enabled {
                let fallback_sender = RpcSender::new(rpc_url.to_string(), client.clone(), skip_preflight);
                match accel_config.provider.as_str() {
                    "astralane" => {
                        info!("🚀 加速器已启用，使用 Astralane 后端。");
                        Box::new(AstralaneSender::new(client, accel_config.clone(), fallback_sender, skip_preflight))
                    },
                    "blockrazor" => {
                        info!("🚀 加速器已启用，使用 BlockRazor 后端。");
                        Box::new(BlockRazorSender::new(client, accel_config.clone(), fallback_sender, skip_preflight))
                    },
                    "flashblock" => {
                        info!("🚀 加速器已启用，使用 Flashblock 后端。");
                        Box::new(FlashblockSender::new(client, accel_config.clone(), fallback_sender, skip_preflight))
                    },
                    "oslot" => {
                        info!("🚀 加速器已启用，使用 Oslot 后端。");
                        Box::new(OslotSender::new(client, accel_config.clone(), fallback_sender, skip_preflight))
                    },
                    _ => {
                        warn!("未知的加速器提供商: '{}'。将回退到标准RPC。", accel_config.provider);
                        Box::new(fallback_sender)
                    }
                }
            } else {
                info!("🐢 未启用加速器，使用标准 RPC 后端。");
                Box::new(RpcSender::new(rpc_url.to_string(), client, skip_preflight))
            }
        };

        Ok(Self {
            backend: std::sync::Arc::from(backend),
        })
    }

    /// 将交易发送到配置好的后端
    pub async fn send_transaction(&self, tx: &Transaction) -> Result<String> {
        self.backend.send_transaction(tx).await
    }

    /// 并发发送4个交易到不同加速器，取最快响应
    pub async fn send_parallel_transactions(&self, transactions: &[Transaction; 4]) -> Result<String> {
        info!("🚀 开始并发发送4个交易到所有加速器");
        
        // 创建所有加速器发送器
        let client = Client::builder()
            .pool_idle_timeout(Some(Duration::from_secs(60)))
            .pool_max_idle_per_host(8)
            .tcp_keepalive(Some(Duration::from_secs(30)))
            .build()?;
            
        let fallback_sender = RpcSender::new("https://api.mainnet-beta.solana.com".to_string(), client.clone(), true);
        
        let astralane_config = AcceleratorConfig {
            enabled: true,
            provider: "astralane".to_string(),
            astralane_api_key: get_accelerator_config().astralane_api_key.clone(),
            blockrazor_api_key: get_accelerator_config().blockrazor_api_key.clone(),
            flashblock_api_key: get_accelerator_config().flashblock_api_key.clone(),
            oslot_api_key: get_accelerator_config().oslot_api_key.clone(),
        };
        
        let blockrazor_config = AcceleratorConfig {
            enabled: true,
            provider: "blockrazor".to_string(),
            astralane_api_key: get_accelerator_config().astralane_api_key.clone(),
            blockrazor_api_key: get_accelerator_config().blockrazor_api_key.clone(),
            flashblock_api_key: get_accelerator_config().flashblock_api_key.clone(),
            oslot_api_key: get_accelerator_config().oslot_api_key.clone(),
        };
        
        let flashblock_config = AcceleratorConfig {
            enabled: true,
            provider: "flashblock".to_string(),
            astralane_api_key: get_accelerator_config().astralane_api_key.clone(),
            blockrazor_api_key: get_accelerator_config().blockrazor_api_key.clone(),
            flashblock_api_key: get_accelerator_config().flashblock_api_key.clone(),
            oslot_api_key: get_accelerator_config().oslot_api_key.clone(),
        };
        
        let oslot_config = AcceleratorConfig {
            enabled: true,
            provider: "oslot".to_string(),
            astralane_api_key: get_accelerator_config().astralane_api_key.clone(),
            blockrazor_api_key: get_accelerator_config().blockrazor_api_key.clone(),
            flashblock_api_key: get_accelerator_config().flashblock_api_key.clone(),
            oslot_api_key: get_accelerator_config().oslot_api_key.clone(),
        };
        
        let astralane_sender = AstralaneSender::new(client.clone(), astralane_config, fallback_sender.clone(), true);
        let blockrazor_sender = BlockRazorSender::new(client.clone(), blockrazor_config, fallback_sender.clone(), true);
        let flashblock_sender = FlashblockSender::new(client.clone(), flashblock_config, fallback_sender.clone(), true);
        let oslot_sender = OslotSender::new(client, oslot_config, fallback_sender, true);
        
        // 并发发送4个交易
        let results = tokio::join!(
            astralane_sender.send_transaction(&transactions[0]),
            blockrazor_sender.send_transaction(&transactions[1]),
            flashblock_sender.send_transaction(&transactions[2]),
            oslot_sender.send_transaction(&transactions[3])
        );
        
        // 返回第一个成功的结果
        if let Ok(sig) = results.0 {
            info!("✅ Astralane发送成功: {}", sig);
            return Ok(sig);
        }
        if let Ok(sig) = results.1 {
            info!("✅ BlockRazor发送成功: {}", sig);
            return Ok(sig);
        }
        if let Ok(sig) = results.2 {
            info!("✅ Flashblock发送成功: {}", sig);
            return Ok(sig);
        }
        if let Ok(sig) = results.3 {
            info!("✅ Oslot发送成功: {}", sig);
            return Ok(sig);
        }
        
        // 所有发送器都失败
        warn!("❌ 所有加速器发送失败");
        warn!("Astralane错误: {:?}", results.0);
        warn!("BlockRazor错误: {:?}", results.1);
        warn!("Flashblock错误: {:?}", results.2);
        warn!("Oslot错误: {:?}", results.3);
        
        Err(anyhow::anyhow!("所有加速器发送失败"))
    }
}

/// 发送轻量 getHealth 请求，用于预热或保活。
async fn send_health_ping(client: &Client, rpc_url: &str) -> Result<()> {
    let body = "{\"jsonrpc\":\"2.0\",\"id\":\"ping\",\"method\":\"getHealth\"}";
    let resp = client
        .post(rpc_url)
        .header(CONTENT_TYPE, "application/json")
        .body(body)
        .send()
        .await?;
    if resp.status().is_success() {
        Ok(())
    } else {
        let s = resp.status();
        let txt = resp.text().await.unwrap_or_default();
        Err(anyhow::anyhow!("保活请求状态异常: {s} - {txt}"))
    }
}

/// 发送轻量HTTP GET请求到加速器端点，用于保活连接
async fn send_accelerator_ping(client: &Client, api_url: &str) -> Result<()> {
    let resp = client
        .get(api_url)
        .timeout(Duration::from_secs(5))
        .send()
        .await?;
    
    if resp.status().is_success() || resp.status().is_client_error() {
        // 对于保活请求，只要能建立连接就认为成功，即使API返回错误状态
        Ok(())
    } else {
        let s = resp.status();
        Err(anyhow::anyhow!("加速器保活连接失败: {s}"))
    }
} 